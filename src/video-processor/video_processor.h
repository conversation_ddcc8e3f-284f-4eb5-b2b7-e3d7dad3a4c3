#pragma once

#include "../iiq-stream/iiq_stream.h"

namespace IQVideoProcessor {

class VideoProcessor {
public:
  explicit VideoProcessor(std::unique_ptr<IIQStream> stream);
  ~VideoProcessor();

  bool initialize();
  void shutdown();

private:
  std::unique_ptr<IIQStream> stream_;
  SampleRateType sampleRate_ = 0; // Sample rate of the video stream

  size_t acqChunkSize_ = 0; // Size of the acquisition chunk
  size_t acqOverlapSize_ = 0; // Overlap size for acquisition chunks
  size_t processingChunkSize_ = 0; // Size of the processing chunk

  bool initialized_;
};

} // namespace IQVideoProcessor
