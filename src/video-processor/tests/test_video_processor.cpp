#include <iostream>
#include <cassert>
#include <stdexcept>
#include <memory>
#include <vector>
#include <thread>
#include <chrono>
#include <atomic>
#include "../video_processor.h"
#include "../pipeline/iq_acquisition_node.h"
#include "../pipeline/iq_async_bridge.h"
#include "../../iiq-stream/iiq_stream.h"

namespace VideoProcessorTests {

// Mock IIQStream for testing
class MockIIQStream : public IIQStream {
public:
    MockIIQStream(SampleRateType sampleRate = 20000000, bool active = true, size_t dataSize = 1000000)
        : sampleRate_(sampleRate), active_(active), sourceName_("mock_test_stream"), readIndex_(0) {
        // Generate test data pattern
        testData_.resize(dataSize);
        for (size_t i = 0; i < dataSize; ++i) {
            testData_[i] = static_cast<SampleType>(i & 0xFFFFFFFF);
        }
    }

    bool readSamples(SampleType* dst, size_t sampleCount) override {
        if (!active_ || readIndex_ >= testData_.size()) return false;

        size_t remainingSamples = testData_.size() - readIndex_;
        size_t samplesToRead = std::min(sampleCount, remainingSamples);

        std::copy(testData_.begin() + readIndex_,
                  testData_.begin() + readIndex_ + samplesToRead, dst);
        readIndex_ += samplesToRead;

        // Fill remaining with zeros if needed
        if (samplesToRead < sampleCount) {
            std::fill(dst + samplesToRead, dst + sampleCount, 0);
        }

        return true;
    }

    SampleRateType sampleRate() const noexcept override { return sampleRate_; }
    const std::string& sourceName() const noexcept override { return sourceName_; }
    bool isActive() const noexcept override { return active_; }
    void close() noexcept override { active_ = false; }
    const std::string& lastError() const noexcept override { return error_; }

    void setActive(bool active) { active_ = active; }
    void resetReadIndex() { readIndex_ = 0; }

private:
    std::vector<SampleType> testData_;
    SampleRateType sampleRate_;
    bool active_;
    std::string sourceName_;
    std::string error_;
    size_t readIndex_;
};

void test_construction_with_valid_stream() {
    std::cout << "Testing VideoProcessor construction with valid stream..." << std::endl;

    auto mockStream = std::make_unique<MockIIQStream>();
    IQVideoProcessor::VideoProcessor processor(std::move(mockStream));

    std::cout << "✓ VideoProcessor constructed successfully with valid stream" << std::endl;
}

void test_construction_with_null_stream() {
    std::cout << "Testing VideoProcessor construction with null stream..." << std::endl;

    IQVideoProcessor::VideoProcessor processor(nullptr);

    // Should handle null stream gracefully
    std::cout << "✓ VideoProcessor constructed with null stream" << std::endl;
}

void test_initialization_with_valid_stream() {
    std::cout << "Testing VideoProcessor initialization with valid stream..." << std::endl;

    auto mockStream = std::make_unique<MockIIQStream>();
    IQVideoProcessor::VideoProcessor processor(std::move(mockStream));

    // Test initialization
    bool result = processor.initialize();
    assert(result == true);
    std::cout << "✓ VideoProcessor initialization successful" << std::endl;

    // Test double initialization (should be idempotent)
    result = processor.initialize();
    assert(result == true);
    std::cout << "✓ Double initialization handled correctly" << std::endl;
}

void test_initialization_with_null_stream() {
    std::cout << "Testing VideoProcessor initialization with null stream..." << std::endl;

    IQVideoProcessor::VideoProcessor processor(nullptr);

    // Should fail with null stream
    bool result = processor.initialize();
    assert(result == false);
    std::cout << "✓ VideoProcessor correctly failed initialization with null stream" << std::endl;
}

void test_shutdown() {
    std::cout << "Testing VideoProcessor shutdown..." << std::endl;

    auto mockStream = std::make_unique<MockIIQStream>();
    IQVideoProcessor::VideoProcessor processor(std::move(mockStream));

    // Initialize first
    processor.initialize();

    // Test shutdown
    processor.shutdown();
    std::cout << "✓ VideoProcessor shutdown successful" << std::endl;

    // Test double shutdown (should be idempotent)
    processor.shutdown();
    std::cout << "✓ Double shutdown handled correctly" << std::endl;
}

void test_lifecycle() {
    std::cout << "Testing VideoProcessor lifecycle..." << std::endl;

    auto mockStream = std::make_unique<MockIIQStream>();
    IQVideoProcessor::VideoProcessor processor(std::move(mockStream));

    // First initialization should succeed
    bool result = processor.initialize();
    assert(result == true);

    // Multiple initializations should be idempotent
    result = processor.initialize();
    assert(result == true);

    // Shutdown
    processor.shutdown();

    // After shutdown, re-initialization should fail because stream was consumed
    result = processor.initialize();
    assert(result == false);

    std::cout << "✓ Lifecycle behavior verified - stream consumption prevents re-initialization" << std::endl;
}

void test_destructor() {
    std::cout << "Testing VideoProcessor destructor..." << std::endl;

    {
        auto mockStream = std::make_unique<MockIIQStream>();
        IQVideoProcessor::VideoProcessor processor(std::move(mockStream));
        processor.initialize();
        // Destructor should be called automatically
    }

    std::cout << "✓ VideoProcessor destructor handled correctly" << std::endl;
}

void test_exception_safety() {
    std::cout << "Testing VideoProcessor exception safety..." << std::endl;

    try {
        auto mockStream = std::make_unique<MockIIQStream>();
        IQVideoProcessor::VideoProcessor processor(std::move(mockStream));
        processor.initialize();
        processor.shutdown();

        std::cout << "✓ No exceptions thrown during normal operation" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "❌ Unexpected exception: " << e.what() << std::endl;
        throw;
    }
}

// Connection and data flow tests
void test_acquisition_node_async_bridge_connection() {
    std::cout << "Testing IQAcquisitionNode to IQAsyncBridge connection..." << std::endl;

    auto mockStream = std::make_unique<MockIIQStream>(20000000, true, 100000);

    // Calculate chunk sizes using the same logic as VideoProcessor
    auto sampleRate = mockStream->sampleRate();
    auto overlapSize = IQVideoProcessor::calcProcessingOverlapSize(sampleRate);
    auto chunkSize = IQVideoProcessor::calcProcessingChunkSize(sampleRate);
    auto acqChunkSize = chunkSize + overlapSize * 2;

    // Create components
    IQVideoProcessor::Pipeline::IQAcquisitionNode acquisitionNode(
        std::move(mockStream), 1024, acqChunkSize, overlapSize);
    IQVideoProcessor::Pipeline::IQAsyncBridge asyncBridge(10);

    // Test connection
    acquisitionNode.connectOutputLink(&asyncBridge);
    std::cout << "✓ Connection established successfully" << std::endl;

    // Set up data capture
    std::atomic<int> chunksReceived{0};
    std::atomic<bool> testComplete{false};

    asyncBridge.onOutput([&chunksReceived, &testComplete](IQVideoProcessor::Pipeline::ComplexIQChunk&& chunk) -> bool {
        chunksReceived++;
        if (chunksReceived >= 3) {
            testComplete = true;
            return false; // Stop processing
        }
        return true;
    });

    // Process some data
    for (int i = 0; i < 10 && !testComplete; ++i) {
        if (!acquisitionNode.tick()) break;

        // Process async bridge
        while (asyncBridge.hasPendingWork() && !testComplete) {
            if (!asyncBridge.tick()) break;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    assert(chunksReceived > 0);
    std::cout << "✓ Data flow verified - received " << chunksReceived << " chunks" << std::endl;
}

void test_connection_error_handling() {
    std::cout << "Testing connection error handling..." << std::endl;

    // Test with null stream
    IQVideoProcessor::Pipeline::IQAcquisitionNode acquisitionNode(nullptr, 0, 0, 0);
    IQVideoProcessor::Pipeline::IQAsyncBridge asyncBridge(10);

    // Connection should work even with null stream
    acquisitionNode.connectOutputLink(&asyncBridge);
    std::cout << "✓ Connection with null stream handled gracefully" << std::endl;

    // Test with null bridge
    auto mockStream = std::make_unique<MockIIQStream>();
    auto sampleRate = mockStream->sampleRate();
    auto overlapSize = IQVideoProcessor::calcProcessingOverlapSize(sampleRate);
    auto chunkSize = IQVideoProcessor::calcProcessingChunkSize(sampleRate);

    IQVideoProcessor::Pipeline::IQAcquisitionNode acquisitionNode2(
        std::move(mockStream), 1024, chunkSize, overlapSize);

    acquisitionNode2.connectOutputLink(nullptr);
    std::cout << "✓ Connection with null bridge handled gracefully" << std::endl;
}

void test_edge_cases() {
    std::cout << "Testing edge cases..." << std::endl;

    // Test with very small data
    auto mockStream = std::make_unique<MockIIQStream>(1000000, true, 100);
    auto sampleRate = mockStream->sampleRate();
    auto overlapSize = IQVideoProcessor::calcProcessingOverlapSize(sampleRate);
    auto chunkSize = IQVideoProcessor::calcProcessingChunkSize(sampleRate);

    IQVideoProcessor::Pipeline::IQAcquisitionNode acquisitionNode(
        std::move(mockStream), 64, chunkSize, overlapSize);
    IQVideoProcessor::Pipeline::IQAsyncBridge asyncBridge(5);

    acquisitionNode.connectOutputLink(&asyncBridge);

    std::atomic<bool> receivedData{false};
    asyncBridge.onOutput([&receivedData](IQVideoProcessor::Pipeline::ComplexIQChunk&& chunk) -> bool {
        receivedData = true;
        return false;
    });

    // Try to process
    for (int i = 0; i < 5; ++i) {
        acquisitionNode.tick();
        asyncBridge.tick();
    }

    std::cout << "✓ Edge case with small data handled" << std::endl;
}

void test_buffer_overflow_handling() {
    std::cout << "Testing buffer overflow handling..." << std::endl;

    auto mockStream = std::make_unique<MockIIQStream>();
    auto sampleRate = mockStream->sampleRate();
    auto overlapSize = IQVideoProcessor::calcProcessingOverlapSize(sampleRate);
    auto chunkSize = IQVideoProcessor::calcProcessingChunkSize(sampleRate);

    IQVideoProcessor::Pipeline::IQAcquisitionNode acquisitionNode(
        std::move(mockStream), 1024, chunkSize, overlapSize);
    IQVideoProcessor::Pipeline::IQAsyncBridge asyncBridge(2); // Very small buffer

    acquisitionNode.connectOutputLink(&asyncBridge);

    std::atomic<int> chunksReceived{0};
    asyncBridge.onOutput([&chunksReceived](IQVideoProcessor::Pipeline::ComplexIQChunk&& chunk) -> bool {
        chunksReceived++;
        // Simulate slow processing
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        return true;
    });

    // Generate data faster than it can be consumed
    for (int i = 0; i < 10; ++i) {
        acquisitionNode.tick();
        // Don't process async bridge immediately to cause buffer buildup
    }

    std::cout << "✓ Buffer overflow scenario handled gracefully" << std::endl;
}

void test_chunk_buffer_reuse() {
    std::cout << "Testing currentChunk_ buffer reuse across processing cycles..." << std::endl;

    auto mockStream = std::make_unique<MockIIQStream>(20000000, true, 500000);
    auto sampleRate = mockStream->sampleRate();
    auto overlapSize = IQVideoProcessor::calcProcessingOverlapSize(sampleRate);
    auto chunkSize = IQVideoProcessor::calcProcessingChunkSize(sampleRate);

    IQVideoProcessor::Pipeline::IQAcquisitionNode acquisitionNode(
        std::move(mockStream), 1024, chunkSize, overlapSize);
    IQVideoProcessor::Pipeline::IQAsyncBridge asyncBridge(10);

    acquisitionNode.connectOutputLink(&asyncBridge);

    std::vector<std::vector<std::complex<ComplexType>>> receivedChunks;
    std::atomic<int> chunksReceived{0};

    asyncBridge.onOutput([&receivedChunks, &chunksReceived](IQVideoProcessor::Pipeline::ComplexIQChunk&& chunk) -> bool {
        // Store a copy of the received chunk data
        receivedChunks.push_back(chunk.data);
        chunksReceived++;

        if (chunksReceived >= 3) {
            return false; // Stop after receiving 3 chunks
        }
        return true;
    });

    // Process multiple cycles to verify buffer reuse
    for (int i = 0; i < 15 && chunksReceived < 3; ++i) {
        acquisitionNode.tick();

        // Process async bridge
        while (asyncBridge.hasPendingWork() && chunksReceived < 3) {
            if (!asyncBridge.tick()) break;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    // Verify we received multiple chunks
    assert(chunksReceived >= 2);
    assert(receivedChunks.size() >= 2);

    // Verify chunks have different data (proving buffer was reused and refilled)
    bool chunksAreDifferent = false;
    if (receivedChunks.size() >= 2) {
        for (size_t i = 0; i < std::min(receivedChunks[0].size(), receivedChunks[1].size()) && i < 10; ++i) {
            if (receivedChunks[0][i] != receivedChunks[1][i]) {
                chunksAreDifferent = true;
                break;
            }
        }
    }

    assert(chunksAreDifferent);
    std::cout << "✓ Buffer reuse verified - received " << chunksReceived << " different chunks" << std::endl;
    std::cout << "✓ currentChunk_ buffer is properly reused across processing cycles" << std::endl;
}

} // namespace VideoProcessorTests

// Main test runner function
int run_video_processor_tests() {
    std::cout << "\n🧪 Running VideoProcessor Comprehensive Tests" << std::endl;
    std::cout << "=============================================" << std::endl;

    try {
        // Basic functionality tests
        VideoProcessorTests::test_construction_with_valid_stream();
        VideoProcessorTests::test_construction_with_null_stream();
        VideoProcessorTests::test_initialization_with_valid_stream();
        VideoProcessorTests::test_initialization_with_null_stream();
        VideoProcessorTests::test_shutdown();
        VideoProcessorTests::test_lifecycle();
        VideoProcessorTests::test_destructor();
        VideoProcessorTests::test_exception_safety();

        // Connection and data flow tests
        VideoProcessorTests::test_acquisition_node_async_bridge_connection();
        VideoProcessorTests::test_connection_error_handling();
        VideoProcessorTests::test_edge_cases();
        VideoProcessorTests::test_buffer_overflow_handling();
        VideoProcessorTests::test_chunk_buffer_reuse();

        std::cout << "\n🎉 All VideoProcessor tests PASSED!" << std::endl;
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "\n❌ VideoProcessor test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "\n❌ VideoProcessor test failed with unknown exception" << std::endl;
        return 1;
    }
}
