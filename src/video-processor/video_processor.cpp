#include "./video_processor.h"
#include "./video_processor_configs.h"
#include "./pipeline/iq_acquisition_node.h"
#include "./pipeline/iq_async_bridge.h"
#include "../configs.h"
#include "../types.h"


class IIQStream;
namespace IQVideoProcessor {

/**
 * HELPER FUNCTIONS
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 */

size_t calcProcessingChunkSize(const SampleRateType sampleRate) {
  const auto samplesPerVideoLine = static_cast<double>(sampleRate) / MIN_LINE_RATE_HZ;
  return static_cast<size_t>(samplesPerVideoLine * LINES_PER_CHUNK);
}

size_t calcProcessingOverlapSize(const SampleRateType sampleRate) {
  constexpr auto ratio = COMPOSITE_SIGNAL_SYNC_SEEK_FILTER_HZ / 2 + 1; // Should be more than 1/2 of the filter frequency
  const auto minimalOverlap = std::lround(sampleRate / ratio);
  return static_cast<size_t>(minimalOverlap) + 1; // Add 1 to ensure we have at least one sample overlap
}

/**
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 * /////////////////////////////////////////////////////////////////////////////////////////////////////////
 */

VideoProcessor::VideoProcessor(std::unique_ptr<IIQStream> stream) : stream_(std::move(stream)), initialized_(false) {

}

VideoProcessor::~VideoProcessor() {
  shutdown();
}

bool VideoProcessor::initialize() {
  if (initialized_) {
    return true;
  }

  if (!stream_) return false;
  if (sampleRate_ = stream_->sampleRate(); sampleRate_ == 0) return false;

  acqOverlapSize_ = calcProcessingOverlapSize(sampleRate_);     // OVLP, contains enough samples for filtering
  processingChunkSize_ = calcProcessingChunkSize(sampleRate_);  // CHUNK (the real data that we process)
  acqChunkSize_ = processingChunkSize_ + acqOverlapSize_ * 2;   // OVLP + CHUNK + OVLP


  Pipeline::IQAcquisitionNode acquisitionNode_1(std::move(stream_), DEFAULT_IQ_STREAM_READ_SIZE, acqChunkSize_, acqOverlapSize_);
  Pipeline::IQAsyncBridge asyncBridge_1(100);

  // acquisitionNode_1.connectOutputLink(&asyncBridge_1);


  initialized_ = true;
  return true;
}

void VideoProcessor::shutdown() {
  if (!initialized_) {
    return;
  }

  initialized_ = false;
}

} // namespace IQVideoProcessor
