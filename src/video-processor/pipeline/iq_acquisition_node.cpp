#include "./iq_acquisition_node.h"

namespace IQVideoProcessor::Pipeline {

#define NUM_OF_WRITE_CHUNKS 16

#pragma pack(push, 1)
struct IQWord {
  int16_t i;   // lower 16 bits (in-phase)
  int16_t q;   // upper 16 bits (quadrature)
};
#pragma pack(pop)

IQAcquisitionNode::IQAcquisitionNode(std::unique_ptr<IIQStream> stream, size_t iqStreamReadSize, size_t iqOutputChunkSize, size_t iqOutputChunkOverlapSize)
  : iqStreamReadSize_(iqStreamReadSize), iqOutputChunkSize_(iqOutputChunkSize),
    iqOutputChunkOverlapSize_(iqOutputChunkOverlapSize), iqStream_(std::move(stream)), samplesProcessed_(0) {

  if (!iqStream_) return; // just do nothing if stream is null

  assert(iqStreamReadSize_ > 0);
  assert(iqOutputChunkSize_ > 0);
  assert(iqOutputChunkOverlapSize_ > 0);


  // Initialize the reusable chunk buffer with sufficient capacity
  currentChunk_.data.resize(iqOutputChunkSize_);

  /*
  * Creating a chunk processor that generates chunks with the pre-calculated sizes
  * passed from VideoProcessor.
  */
  chunkProcessor_ = std::make_unique<ChunkProcessor<SampleType>>(
    iqStreamReadSize_,
    iqOutputChunkSize_,
    iqOutputChunkOverlapSize_,
    NUM_OF_WRITE_CHUNKS,
    ([this](const SampleType *buffer, const size_t chunkSize, const size_t overlapSize) {
      this->onOutputChunkReady(buffer, chunkSize, overlapSize);
    })
  );
  // Making the node running
  setRunning();
}

IQAcquisitionNode::~IQAcquisitionNode() {
  PipelineComponent::stop();
}

bool IQAcquisitionNode::process(bool &&input) {
  return false; // We don't process input directly in this node
}

bool IQAcquisitionNode::hasPendingWork() const {
  return false;
}

bool IQAcquisitionNode::tick() {
  if (!running()) {
    return false; // Node is not running
  }

  auto *buffer = chunkProcessor_->getWriteChunkPtr();
  if (!iqStream_->readSamples(buffer, iqStreamReadSize_)) {
    stop();
    return false;
  }

  if (!running()) {
    return false; // Node is not running
  }

  chunkProcessor_->commitWriteChunk();
  return true;
}

void IQAcquisitionNode::onOutputChunkReady(const SampleType *chunkBuffer, const size_t chunkSize, const size_t overlapSize) {
  if (!running()) return;

  constexpr ComplexType kScale = 1.0f / 32768.0f; // Normalization scale for int16_t range

  auto *inputIQBuffer = reinterpret_cast<const IQWord *>(chunkBuffer);

  for (size_t i = 0; i < chunkSize; ++i) {
    currentChunk_.data[i].real(static_cast<ComplexType>(inputIQBuffer[i].i) * kScale);
    currentChunk_.data[i].imag(static_cast<ComplexType>(inputIQBuffer[i].q) * kScale);
  }

  // Update chunk metadata
  currentChunk_.size = chunkSize;
  currentChunk_.overlap = overlapSize;

  if (this->sendOutput(std::move(currentChunk_))) {
    samplesProcessed_ += chunkSize - overlapSize; // Update processed samples count
    // Prepare currentChunk_ for next use
    currentChunk_.data.resize(iqOutputChunkSize_);
  } else {
    // If output link rejected the data, we should stop processing
    stop();
  }
}

} // namespace IQVideoProcessor::Pipeline

