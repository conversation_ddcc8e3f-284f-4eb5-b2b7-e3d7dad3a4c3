#pragma once

// IQ Stream configs
constexpr size_t DEFAULT_IQ_STREAM_READ_SIZE            = 8 * 1024;         // Reading the IQ stream in chunks of 8k samples

// IQ Stream chunk configs
constexpr double MIN_LINE_RATE_HZ                       = 15000.0;          // Minimum line rate for video processing (auto)
constexpr double LINES_PER_CHUNK                        = 5.0;              // Put 5 lines per chunk for processing

